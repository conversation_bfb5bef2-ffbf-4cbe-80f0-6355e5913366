# LEKCE 2: PROMĚNNÉ A DATOVÉ TYPY
# ================================

"""
Proměnné jsou jako "krabičky" kde si ukládáš hodnoty.
V <PERSON>u nemus<PERSON>, jak<PERSON>ho typu proměnná je - Python to pozná sám!

Tato lekce tě naučí:
1. Jak vytvářet proměnné
2. <PERSON><PERSON>lad<PERSON><PERSON> datové typy
3. Jak s nimi pracovat
"""

# ===== CO JSOU PROMĚNNÉ =====
# Proměnná = jméno pro hodnotu, kterou chceš použít později

jmeno = "Adam"          # Řetězec (text)
vek = 22               # Celé číslo
vyska = 1.80           # Desetinné číslo
je_student = True      # Boolean (pravda/nepravda)

print("Jméno:", jmeno)
print("Věk:", vek)
print("Výška:", vyska)
print("Je student:", je_student)

# ===== DATOVÉ TYPY =====

# 1. ŘETĚZCE (STRING) - text v uvozovkách
mesto = "Praha"
zeme = 'Česká republika'  # Můžeš použít ' nebo "
dlouhy_text = """Toto je
velmi dlouhý
text na více řádků"""

print("\nŘetězce:")
print(mesto)
print(zeme)

# 2. ČÍSLA
# Celá čísla (int)
pocet_prstů = 10
teplota = -5

# Desetinná čísla (float)
pi = 3.14159
cena = 99.90

print("\nČísla:")
print("Prsty:", pocet_prstů)
print("Teplota:", teplota)
print("Pi:", pi)

# 3. BOOLEAN - pouze True nebo False
je_slunecno = True
prsi = False
je_teplo = vek > 18  # Výsledek porovnání

print("\nBoolean:")
print("Slunečno:", je_slunecno)
print("Prší:", prsi)
print("Je dospělý:", je_teplo)

# ===== ZÁKLADNÍ OPERACE =====

# S čísly
a = 10
b = 3

print("\nMatematické operace:")
print("Sčítání:", a + b)      # 13
print("Odčítání:", a - b)     # 7
print("Násobení:", a * b)     # 30
print("Dělení:", a / b)       # 3.333...
print("Celočíselné dělení:", a // b)  # 3
print("Zbytek po dělení:", a % b)     # 1
print("Mocnina:", a ** b)     # 1000

# S řetězci
krestni = "Adam"
prijmeni = "Hradil"
cele_jmeno = krestni + " " + prijmeni  # Spojování řetězců

print("\nPráce s řetězci:")
print("Celé jméno:", cele_jmeno)
print("Opakování:", "Ha" * 3)  # HaHaHa

# ===== ZJIŠTĚNÍ TYPU PROMĚNNÉ =====
print("\nTypy proměnných:")
print("Typ jména:", type(jmeno))
print("Typ věku:", type(vek))
print("Typ výšky:", type(vyska))
print("Typ boolean:", type(je_student))

# ===== CVIČENÍ PRO TEBE =====
"""
ÚKOL 1: Vytvoř proměnné pro své oblíbené:
- barvu (řetězec)
- číslo (celé číslo)
- film (řetězec)

ÚKOL 2: Spočítej a vypiš:
- Kolik budeš mít let za 10 let
- Tvůj věk krát 2
- Tvůj věk děleno 2

ÚKOL 3: Vytvoř boolean proměnnou, která říká, jestli:
- Máš více než 20 let
- Tvoje oblíbené číslo je sudé (použij % 2 == 0)
"""

# Zde napiš své řešení:
# oblibena_barva = 
# oblibene_cislo = 
# oblibeny_film = 

# vek_za_10_let = 
# vek_krat_2 = 
# vek_deleno_2 = 

# je_starsi_20 = 
# cislo_je_sude = 
