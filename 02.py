# LEKCE 2: PROMĚNNÉ A DATOVÉ TYPY
# ================================

"""
Proměnné jsou jako "krabičky" kde si ukládáš hodnoty.
V Pythonu nemus<PERSON>, jak<PERSON>ho typu proměnná je - Python to pozná sám!

Tato lekce tě naučí:
1. Jak vytvářet proměnné
2. <PERSON><PERSON>lad<PERSON><PERSON> datové typy
3. Jak s nimi pracovat
"""

# ===== CO JSOU PROMĚNNÉ =====
# Proměnná = jméno pro hodnotu, kterou chceš použít později 

# ===== DATOVÉ TYPY =====

# 1. ŘETĚZCE (STRING) - text v uvozovkách

# 2. ČÍSLA
# Celá čísla (int)
# Desetinná čísla (float)

# 3. BOOLEAN - pouze True nebo False

# ===== ZÁKLADNÍ OPERACE =====

# S čísly

# S řetězci

# ===== ZJIŠTĚNÍ TYPU PROMĚNNÉ =====

# ===== CVIČENÍ PRO TEBE =====
"""
ÚKOL 1: Vytvoř proměnné pro své oblíbené:
- barvu (řetězec)
- číslo (celé číslo)
- film (řetězec)

ÚKOL 2: Spočítej a vypiš:
- Kolik budeš mít let za 10 let
- Tvůj věk krát 2
- Tvůj věk děleno 2

ÚKOL 3: Vytvoř boolean proměnnou, která říká, jestli:
- Máš více než 20 let
- Tvoje oblíbené číslo je sudé (použij % 2 == 0)
"""
oblibena_barva= "modrá"

print(oblibena_barva)