# Python Základy - <PERSON><PERSON> pro začátečníky

Vítej v kurzu základů Pythonu! Tento kurz tě provede od úplných základů až po pokročilejší koncepty.

## Jak používat tento kurz

1. **Každá lekce je v samostatném souboru** - za<PERSON><PERSON> `lekce_01_uvod.py`
2. **Čti komentáře** - v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, co kód dělá
3. **Spouštěj kód** - vyzkoušej si každý příklad
4. **Dělej cvičení** - jsou na konci každé lekce
5. **Ptej se** - pokud něčemu nerozumíš

## Jak spustit Python kód

### Možnost 1: V termin<PERSON>lu
```bash
python lekce_01_uvod.py
```

### Možnost 2: Interaktivně
```bash
python
>>> print("Ahoj!")
```

## P<PERSON><PERSON> lek<PERSON>í

- **Lekce 1**: Úvod a první program ✅
- **Lekce 2**: Proměnné a datové typy
- **Lekce 3**: Práce s řetězci
- **Lekce 4**: Seznamy a tuple
- **Lekce 5**: Slovníky a množiny
- **Lekce 6**: Podmínky a logika
- **Lekce 7**: Cykly
- **Lekce 8**: Funkce

## Tipy pro začátečníky

- **Neboj se experimentovat** - Python je bezpečný
- **Čti chybové hlášky** - často ti řeknou, co je špatně
- **Procvičuj pravidelně** - i 15 minut denně pomůže
- **Piš kód ručně** - nekopíruj, ale přepiš si příklady

Hodně štěstí při učení! 🐍
